const { Client, LocalAuth, MessageMedia } = require("whatsapp-web.js");
const qrcode = require("qrcode-terminal");
const chalk = require("chalk");
const dotenv = require("dotenv");
const path = require("node:path");
const fs = require("node:fs");

// Import our modules
const { generateResponse } = require("./services/llm");
const { generateSpeechFromText } = require("./services/generateSpeech");
const { generateImageGemini } = require("./services/generateImageGemini");
const { formatMessage } = require("./utils/messageFormatter");
const { RateLimiter } = require("./utils/rateLimiter");
const {
  processSlashCommand,
  isSlashCommand,
} = require("./services/slashCommands");
const logger = require("./utils/logger");
const {
  isMentioned,
  extractQuestion,
  getMessageContext,
  getContactDisplayName,
  formatMessageDateTime,
} = require("./utils/messageParser");
const { WebServer } = require("./services/webServer");
const tokenCounter = require("openai-gpt-token-counter");
const { configService } = require("./services/configService");

// Load environment variables
const envPath = path.join(__dirname, "..", ".env");
if (fs.existsSync(envPath)) {
  dotenv.config({ path: envPath });
} else {
  logger.error("No .env file found. Please create one based on .env.example");
  logger.info("Creating .env file from example...");
  fs.copyFileSync(path.join(__dirname, "..", ".env.example"), envPath);
  dotenv.config({ path: envPath });
  logger.success(
    ".env file created! Please update it with your API keys and restart the bot."
  );
  process.exit(1);
}

// Configuration - using configService for dynamic updates
let BOT_NAME = configService.get("botName");
let GROUP_ONLY = configService.get("groupOnly");
let RATE_LIMIT = configService.get("rateLimit");
let RATE_LIMIT_WINDOW = configService.get("rateLimitWindow");
let MAX_CONTEXT_MESSAGES = configService.get("maxContextMessages");

// Initialize rate limiter
let rateLimiter = new RateLimiter(RATE_LIMIT, RATE_LIMIT_WINDOW);

// Listen for configuration changes
configService.addListener((key, newValue, oldValue) => {
  logger.info(`Configuration changed: ${key} = ${newValue} (was ${oldValue})`);

  switch (key) {
    case "botName":
      BOT_NAME = newValue;
      break;
    case "groupOnly":
      GROUP_ONLY = newValue;
      break;
    case "rateLimit":
      RATE_LIMIT = newValue;
      rateLimiter = new RateLimiter(RATE_LIMIT, RATE_LIMIT_WINDOW);
      break;
    case "rateLimitWindow":
      RATE_LIMIT_WINDOW = newValue;
      rateLimiter = new RateLimiter(RATE_LIMIT, RATE_LIMIT_WINDOW);
      break;
    case "maxContextMessages":
      MAX_CONTEXT_MESSAGES = newValue;
      break;
  }
});

// Initialize web server
const webServer = new WebServer();

// Helper function to introduce delay
const sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));
const RESPONSE_DELAY = 4000; // 4 seconds delay before responding

// Use a completely static client ID for reliable auth persistence
const clientId = "whatsapp-llm-bot-stable";

// Initialize WhatsApp client with Docker-specific configuration
const client = new Client({
  authStrategy: new LocalAuth({
    clientId: clientId,
    dataPath: "/usr/src/app/.wwebjs_auth",
  }),
  puppeteer: {
    headless: true,
    args: [
      "--no-sandbox",
      "--disable-setuid-sandbox",
      "--disable-dev-shm-usage",
      "--disable-accelerated-2d-canvas",
      "--no-first-run",
      "--no-zygote",
      "--disable-gpu",
      "--disable-extensions",
      "--disable-background-timer-throttling",
      "--disable-backgrounding-occluded-windows",
      "--disable-renderer-backgrounding",
      "--disable-features=TranslateUI",
      "--disable-ipc-flooding-protection",
      "--force-device-scale-factor=1",
      "--disable-features=VizDisplayCompositor",
      "--no-default-browser-check",
      "--disable-default-apps",
      "--disable-sync",
      "--disable-translate",
      "--hide-scrollbars",
      "--mute-audio",
      "--no-crash-upload",
      "--disable-logging",
      "--disable-login-animations",
      "--disable-notifications",
    ],
    executablePath: process.env.PUPPETEER_EXECUTABLE_PATH || undefined,
  },
});

// QR code generation event
client.on("qr", async (qr) => {
  logger.info("Scan the QR code below to log in to WhatsApp Web:");
  logger.info(
    `Or visit: http://localhost:${webServer.port} to scan via web interface`
  );

  // Display in console
  qrcode.generate(qr, { small: true });

  // Update web server with QR code
  await webServer.updateQRCode(qr);
  webServer.updateStatus("qr_ready");
});

// Authentication successful
client.on("authenticated", () => {
  logger.success("Authentication successful!");
  webServer.updateStatus("authenticated");
  webServer.clearQRCode();
});

// Authentication failure
client.on("auth_failure", (error) => {
  logger.error("Authentication failed:", error);
});

// Client ready
client.on("ready", () => {
  logger.success(`WhatsApp bot is ready! Bot name: ${chalk.green(BOT_NAME)}`);
  if (GROUP_ONLY) {
    logger.info("Bot will only respond in group chats");
  }
  webServer.updateStatus("ready");
});

// Message handler
client.on("message", async (message) => {
  try {
    // Check if message is from a group if GROUP_ONLY is enabled
    if (GROUP_ONLY) {
      const chat = await message.getChat();
      if (!chat.isGroup) return;
    }

    // Get chat info for random events
    const chat = await message.getChat();

    // Check for slash commands first (these work regardless of mention/reply status)
    const messageBody = message.body.trim();
    const hasSlashCommand = isSlashCommand(messageBody);

    // Check if the bot is mentioned OR if it's a media message (auto-analyze images/audio)
    const isBotMentioned = await isMentioned(message, BOT_NAME, client);
    const isMediaMessage = message.hasMedia && message.body.trim() === "";
    const isDirectChat = !chat.isGroup;

    // Check if this message is a reply to a bot message
    let isReplyToBotMessage = false;
    if (message.hasQuotedMsg) {
      try {
        const quotedMessage = await message.getQuotedMessage();
        const botContact = await client.getContactById(
          client.info.wid._serialized
        );
        isReplyToBotMessage =
          quotedMessage.author === botContact.id._serialized ||
          quotedMessage.from === botContact.id._serialized;
      } catch (error) {
        logger.warn("Error checking if quoted message is from bot:", error);
      }
    }

    // In direct chats, respond to any message; in group chats, only respond if mentioned, replying to bot, or using slash commands
    const shouldRespond =
      isDirectChat || isBotMentioned || isReplyToBotMessage || hasSlashCommand;

    if (shouldRespond) {
      // Apply rate limiting
      const chatId = message.from;
      if (!rateLimiter.tryAcquire(chatId)) {
        logger.warn(`Rate limit exceeded for chat ${chatId}`);
        message.reply(
          "I'm receiving too many requests. Please try again in a minute."
        );
        return;
      }

      // Handle slash commands
      let question = "";
      let isVoiceNote = false;
      let isImageGeneration = false;
      let isImageEditing = false;
      let originalUserMessage = "";

      // Process slash command if present
      if (hasSlashCommand) {
        const commandResult = await processSlashCommand(message, messageBody);

        if (commandResult.handled) {
          return; // Command was fully handled (like help command)
        }

        // Extract command results
        question = commandResult.question;
        isVoiceNote = commandResult.isVoiceNote;
        isImageGeneration = commandResult.isImageGeneration;
        isImageEditing = commandResult.isImageEditing;

        // Store original message for voice style detection
        if (isVoiceNote) {
          originalUserMessage = messageBody;
        }
      } else {
        // Extract the question from the message using existing logic
        question = await extractQuestion(message, BOT_NAME);

        // In direct chats, if no question was extracted, use the full message body
        if (!question && isDirectChat && message.body.trim()) {
          question = message.body.trim();
        }

        // If no question was extracted but it's a media message, provide default question
        if (!question && isMediaMessage) {
          question = "What do you think about this media?";
        } else if (!question) {
          message.reply(`Hi! I'm ${BOT_NAME}. How can I help you?`);
          return;
        }

        // Check for image generation request (legacy support)
        isImageGeneration = question
          .toLowerCase()
          .includes("generate an image");
        isImageEditing = question.toLowerCase().includes("edit this image");

        // Check for voice note request (legacy support)
        const isLegacyVoiceNote = question.toLowerCase().startsWith("!voice");
        if (isLegacyVoiceNote) {
          isVoiceNote = true;
          originalUserMessage = question; // Store original before trimming
          question = question.substring("!voice".length).trim();
        }
      }

      // Check if the message has media (image/audio) or if it's a reply to a message with media
      let imageBuffer = null;
      let audioBuffer = null;
      let audioMimeType = null;
      let imageSource = null;

      // First check if the current message has media
      if (message.hasMedia) {
        try {
          const media = await message.downloadMedia();

          // Check if it's an image
          if (media.mimetype.startsWith("image/")) {
            imageBuffer = Buffer.from(media.data, "base64");
            imageSource = "current message";
            logger.info(
              `Processing image from current message with question: "${question}"`
            );
          } else if (media.mimetype.startsWith("audio/")) {
            audioBuffer = Buffer.from(media.data, "base64");
            audioMimeType = media.mimetype;
            logger.info(
              `Processing audio from current message with question: "${question}"`
            );
          } else {
            logger.warn(`Unsupported media type: ${media.mimetype}`);
            message.reply(
              "I can only process images and audio at the moment. Please send an image or audio along with your question, or ask me a text question."
            );
            return;
          }
        } catch (error) {
          logger.error("Error downloading media from current message:", error);
          message.reply(
            "Sorry, I couldn't process the media you sent. Please try again."
          );
          return;
        }
      }
      // Check if this is a reply to a message with media
      else if (message.hasQuotedMsg) {
        try {
          const quotedMessage = await message.getQuotedMessage();

          if (quotedMessage.hasMedia) {
            const media = await quotedMessage.downloadMedia();

            // Check if it's an image
            if (media.mimetype.startsWith("image/")) {
              imageBuffer = Buffer.from(media.data, "base64");
              imageSource = "replied message";
              logger.info(
                `Processing image from replied message with question: "${question}"`
              );
            } else if (media.mimetype.startsWith("audio/")) {
              audioBuffer = Buffer.from(media.data, "base64");
              audioMimeType = media.mimetype;
              logger.info(
                `Processing audio from replied message with question: "${question}"`
              );
            } else {
              logger.info(
                `Unsupported media type in replied message: ${media.mimetype}, proceeding with text-only response`
              );
            }
          } else {
            // If the quoted message doesn't have media, we can still proceed with text-only response
            logger.info(
              "Reply message without media, proceeding with text-only response"
            );
          }
        } catch (error) {
          logger.error("Error downloading media from quoted message:", error);
          message.reply(
            "Sorry, I couldn't process the media from the message you replied to. Please try again."
          );
          return;
        }
      }

      // Get the current user's information
      const contact = await message.getContact();
      const currentUserName = await getContactDisplayName(contact);

      // Get chat context (previous messages)
      const contextMessages = await getMessageContext(
        chat,
        BOT_NAME,
        MAX_CONTEXT_MESSAGES,
        message
      );

      // Include current user information with the question and timestamp
      const currentMessageDateTime = formatMessageDateTime(message.timestamp);
      const questionWithUser = `${currentUserName} [${currentMessageDateTime}]:\n${question}`;

      // Set typing state
      chat.sendStateTyping();

      try {
        // Handle image generation and editing requests
        if (isImageGeneration || isImageEditing) {
          if (isImageGeneration) {
            logger.info(`Generating image for: "${question}"`);

            // For slash commands, use the question directly; for legacy commands, extract the prompt
            let imagePrompt = question;
            if (!hasSlashCommand) {
              // Legacy "generate an image" handling
              imagePrompt = question
                .toLowerCase()
                .replace(/generate an image\s*(of|for|with)?\s*/gi, "")
                .trim();
            }

            // If no specific prompt is provided, ask for clarification
            if (!imagePrompt) {
              await message.reply(
                "Please provide a description of what you'd like me to generate. For example: '/image a sunset over mountains' or 'generate an image of a sunset over mountains'"
              );
              return;
            }

            try {
              const generatedImageBuffer =
                await generateImageGemini(imagePrompt);
              if (!generatedImageBuffer) {
                await message.reply(
                  "It won't let me do that because it's 'too sensitive' or something. Please try again with a different prompt."
                );
                return;
              }

              const media = new MessageMedia(
                "image/png",
                generatedImageBuffer.toString("base64")
              );
              await client.sendMessage(message.from, media);

              logger.success(
                `Image generated and sent for prompt: "${imagePrompt}"`
              );
              return; // Exit early since we've handled the image generation
            } catch (imageError) {
              logger.error("Error generating image:", imageError);
              await message.reply(
                "Sorry, I encountered an error while generating the image. Please try again later."
              );
              return;
            }
          } else if (isImageEditing) {
            logger.info(`Editing image for: "${question}"`);

            // Check if we have an image to edit
            if (!imageBuffer) {
              await message.reply(
                "Please send an image or reply to a message with an image to edit it. For example: send an image and say 'edit this image to make it black and white'"
              );
              return;
            }

            // Extract the editing prompt (remove "edit this image" part)
            const editPrompt = question
              .toLowerCase()
              .replace(/edit this image\s*(to|and)?\s*/gi, "")
              .trim();

            // If no specific editing instruction is provided, ask for clarification
            if (!editPrompt) {
              await message.reply(
                "Please provide instructions on how you'd like me to edit the image. For example: 'edit this image to make it black and white' or 'edit this image to add a sunset background'"
              );
              return;
            }

            try {
              const editedImageBuffer = await generateImageGemini(
                editPrompt,
                imageBuffer
              );
              if (!editedImageBuffer) {
                await message.reply(
                  "I couldn't edit the image as requested. Please try again with different instructions."
                );
                return;
              }

              const media = new MessageMedia(
                "image/png",
                editedImageBuffer.toString("base64")
              );
              await client.sendMessage(message.from, media);

              logger.success(
                `Image edited and sent for prompt: "${editPrompt}"`
              );
              return; // Exit early since we've handled the image editing
            } catch (imageError) {
              logger.error("Error editing image:", imageError);
              await message.reply(
                "Sorry, I encountered an error while editing the image. Please try again later."
              );
              return;
            }
          }
        }

        // Generate a response using the LLM with user context, previous messages, and optional image/audio
        const responseText = await generateResponse(
          questionWithUser,
          contextMessages,
          imageBuffer,
          audioBuffer,
          audioMimeType
        );

        if (responseText !== "NO_REPLY") {
          if (isVoiceNote) {
            logger.info(`Generating voice note for: "${responseText}"`);
            const { audioBuffer, contentType } = await generateSpeechFromText(
              responseText,
              originalUserMessage
            );
            const media = new MessageMedia(
              contentType || "audio/mpeg",
              audioBuffer.toString("base64")
            );
            await client.sendMessage(message.from, media, {
              sendAudioAsVoice: true,
            });
            logger.success("Voice note sent successfully!");
          } else {
            // Format and send the response
            const formattedResponse = await formatMessage(responseText);
            await sleep(RESPONSE_DELAY);
            await message.reply(formattedResponse);
          }
        }
        logger.info(
          "Context messages token amount:",
          tokenCounter.chat(contextMessages, "gpt-4")
        );
      } catch (error) {
        logger.error("Error generating or sending response:", error);
        message.reply(
          "Sorry, I encountered an error while processing your request."
        );
      } finally {
        // Clear typing state
        chat.clearState();
      }

      // Log the interaction
      const replyContext = isReplyToBotMessage ? " (replying to bot)" : "";
      const logMessage = imageBuffer
        ? `Responded to ${currentUserName}${replyContext} with image from ${imageSource}: "${question.substring(0, 50)}${question.length > 50 ? "..." : ""}"`
        : `Responded to ${currentUserName}${replyContext}: "${question.substring(0, 50)}${question.length > 50 ? "..." : ""}"`;
      logger.info(logMessage);
    }
  } catch (error) {
    logger.error("Error handling message:", error);
    // message.react('❌');
    message.reply(
      "Sorry, I encountered an error while processing your request."
    );
  }
});

// Start the web server and client
async function start() {
  try {
    logger.info(`Using client ID: ${clientId}`);

    // Start web server first
    await webServer.start();

    // Then initialize WhatsApp client
    await client.initialize();
  } catch (error) {
    logger.error("Failed to start application:", error);

    // If it's a Chrome singleton error, try retrying once
    if (error.message?.includes("profile appears to be in use")) {
      logger.info(
        "Detected WhatsApp Web session conflict, cleaning up and retrying..."
      );

      // Wait a moment and try once more
      setTimeout(async () => {
        try {
          await client.initialize();
        } catch (retryError) {
          logger.error("Retry failed:", retryError);
          process.exit(1);
        }
      }, 3000);
    } else {
      process.exit(1);
    }
  }
}

start();

// Handle graceful shutdown
const shutdown = async (signal) => {
  logger.info(`Received ${signal}. Shutting down gracefully...`);
  try {
    await webServer.stop();
    await client.destroy();

    logger.success("Shutdown completed successfully");
  } catch (error) {
    logger.error("Error during shutdown:", error);
  }
  process.exit(0);
};

process.on("SIGINT", () => shutdown("SIGINT"));
process.on("SIGTERM", () => shutdown("SIGTERM"));

// Handle uncaught exceptions
process.on("uncaughtException", (error) => {
  logger.error("Uncaught Exception:", error);
  shutdown("uncaughtException");
});

process.on("unhandledRejection", (reason, promise) => {
  logger.error("Unhandled Rejection at:", promise, "reason:", reason);
  shutdown("unhandledRejection");
});
